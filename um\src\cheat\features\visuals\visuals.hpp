#pragma once
#include "../../../driver/driver.hpp"
#include "../../../math/vector.hpp" // <PERSON><PERSON><PERSON><PERSON>, dass view_matrix_t hier oder in driver.hpp definiert ist
#include "../../entity.hpp"
#include "../../gamevars.hpp"
#include "../../gamedata.hpp" // GameData convenience wrapper system
#include "../../../render/render.hpp"
#include "../../animations/esp/health_animations.hpp"
#include "../../animations/esp/armor_animations.hpp"
#include "../../animations/esp/death_animations.hpp"
#include "../../bones.hpp"
#include "../../globals.hpp"
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <chrono>

#define MAX_BONE_LENGTH 100.0f  // Maximum bone length for skeleton drawing
inline float fontSize = 9.0f;

class VISUALS {
  // Allow DeathAnimations to access private drawing functions
  friend class DeathAnimations;

public:
  static void RenderESP( HANDLE driverHandle, const Reader& reader );

private:
  // Drawing functions are now private again - using GameData for viewMatrix
  static void DrawPlayerBox( const Vector& screenHead, const Vector& screenFeet );
  static void DrawPlayerCorneredBox( const Vector& screenHead, const Vector& screenFeet );
  static void DrawPlayerInfo( const Vector& screenHead, const Vector& screenFeet, std::string PlayerName, uint32_t PlayerFlags, uint16_t ItemDefinitionIndex );
  static void DrawPlayerSnapline( const Vector& screenHead, const Vector& screenFeet );
  static void DrawPlayerHealth( const Vector& screenHead, float boxHeight, float boxHalfWidth, int health, int entityId );
  static void DrawPlayerArmor( const Vector& screenHead, float boxHeight, float boxHalfWidth, int armor, int entityId );
  static void DrawPlayerFilledBox( const Vector& screenHead, const Vector& screenFeet );

  static void DrawPlayerHealthBarReactive( const Vector& top, float height, float width, int health, int entityId );
  static void DrawPlayerHealthBarSolid( const Vector& top, float height, float width, int health, int entityId );

  static void DrawViewline( const Vector& viewangle, const Vector& entityhead );
  static void DrawPlayerSkeleton( uint64_t boneArray );
  static void DrawPlayerJoints( uint64_t boneArray );
  static void DrawPlayerHead( uint64_t boneArray );
  static void DrawProjectile( const Vector& screenPos, std::string name );

  static void DrawAimbotCircle();

  static void Darkmode();
};
